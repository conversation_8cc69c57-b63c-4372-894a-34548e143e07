#!/usr/bin/env python3
"""
Quick test to verify CLI dataset command works without hanging.
"""

import sys
import os
import subprocess
import time

def test_cli_datasets():
    """Test the CLI dataset command."""
    print("Testing CLI dataset command...")
    
    try:
        # Run the CLI command with a timeout
        start_time = time.time()
        
        result = subprocess.run([
            sys.executable, "-m", "chatBIS",
            "query", "--data", "data/processed", "--query", "give me all my datasets"
        ],
        cwd=os.getcwd(),
        capture_output=True, 
        text=True, 
        timeout=60  # 60 second timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"CLI command completed in {duration:.2f} seconds")
        
        if result.returncode == 0:
            print("✓ CLI command succeeded")
            
            # Show first few lines of output
            output_lines = result.stdout.split('\n')[:10]
            print("Sample output:")
            for line in output_lines:
                if line.strip():
                    print(f"  {line}")
                    
            return True
        else:
            print(f"✗ CLI command failed with return code {result.returncode}")
            print("STDOUT:", result.stdout[:500])
            print("STDERR:", result.stderr[:500])
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ CLI command timed out (hang detected)")
        return False
    except Exception as e:
        print(f"✗ CLI test failed: {e}")
        return False

def main():
    print("CLI Dataset Command Test")
    print("=" * 40)
    
    success = test_cli_datasets()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ CLI dataset command works without hanging!")
    else:
        print("❌ CLI dataset command still has issues")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
