#!/usr/bin/env python3
"""
Test script for the chatBIS structured workflow with actual conversation engine.

This script tests the integration of the new structured workflow in a more
realistic scenario, simulating actual user interactions.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_conversation_engine_structured_workflow():
    """Test the ConversationEngine with structured workflow."""
    print("=== Testing ConversationEngine Structured Workflow ===")
    
    try:
        from chatBIS.query.conversation_engine import ConversationEngine
        
        # Initialize with test data directory
        test_data_dir = "data/processed"
        
        engine = ConversationEngine(data_dir=test_data_dir, model="qwen3")
        
        # Create a session
        session_id = engine.create_session()
        print(f"✅ Created session: {session_id}")
        
        # Test queries that should use structured workflow
        test_queries = [
            "Create a new project called TEST_PROJECT_123",
            "Make a sample named TEST_SAMPLE_456", 
            "Get the properties of sample CHEM208",
            "Give me my last experiment"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing query: '{query}'")
            try:
                response, returned_session_id, metadata = engine.chat(query, session_id)
                
                print(f"✅ Response received (decision: {metadata.get('decision', 'unknown')})")
                print(f"📝 Response preview: {response[:200]}...")
                
                # Check if it used structured workflow
                if metadata.get('decision') == 'structured_interaction':
                    print("✅ Used structured interaction workflow")
                elif metadata.get('decision') == 'function_call':
                    print("ℹ️ Used function call workflow")
                elif metadata.get('decision') == 'rag':
                    print("ℹ️ Used RAG workflow")
                else:
                    print(f"⚠️ Unknown decision: {metadata.get('decision')}")
                
            except Exception as e:
                print(f"❌ Query failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ ConversationEngine test failed: {e}")
        return False


def test_entity_structurer_with_real_queries():
    """Test EntityStructurer with real user queries."""
    print("\n=== Testing EntityStructurer with Real Queries ===")
    
    try:
        from chatBIS.tools.entity_structurer import EntityStructurer
        
        structurer = EntityStructurer(model="qwen3")
        
        # Test queries from the conversation log
        real_queries = [
            "create a new experiment called CHAT_EXP",
            "create a new sample \"CHAT_SAMPLE\"",
            "Create a new project called CHAT_PROJECT",
            "give me my last sample",
            "give me the properties of sample CHEM208",
            "give me the samples in the experiment MYCHEMICALS"
        ]
        
        for query in real_queries:
            print(f"\n🔍 Structuring: '{query}'")
            try:
                result = structurer.structure_user_request(query)
                actions = result.get('actions', [])
                
                print(f"✅ Structured into {len(actions)} actions:")
                for i, action in enumerate(actions, 1):
                    action_type = action.get('action', 'UNKNOWN')
                    entity_type = action.get('entity', 'UNKNOWN')
                    
                    if action_type == 'CREATE':
                        code = action.get('payload', {}).get('code', 'UNKNOWN')
                        print(f"   {i}. {action_type} {entity_type}: {code}")
                    elif action_type in ['GET', 'LIST']:
                        identifier = action.get('identifier', {})
                        if identifier:
                            print(f"   {i}. {action_type} {entity_type}: {identifier}")
                        else:
                            print(f"   {i}. {action_type} {entity_type}")
                    else:
                        print(f"   {i}. {action_type} {entity_type}")
                
                # Test confirmation summary
                summary = structurer.format_confirmation_summary(result)
                print(f"✅ Confirmation summary generated ({len(summary)} chars)")
                
            except Exception as e:
                print(f"❌ Failed to structure: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ EntityStructurer test failed: {e}")
        return False


def test_pybis_adapter_space_resolution():
    """Test PybisAdapter space resolution logic."""
    print("\n=== Testing PybisAdapter Space Resolution ===")
    
    try:
        from chatBIS.tools.pybis_adapter import PybisAdapter
        from chatBIS.models.entity import Action, Location, Payload
        
        adapter = PybisAdapter()
        
        # Create a mock pybis instance
        class MockPybisInstance:
            def __init__(self):
                self.username = "testuser"
            
            def get_projects(self, space=None):
                class MockProject:
                    def __init__(self, identifier):
                        self.identifier = identifier
                        self.registrationDate = "2024-01-01"
                
                return [MockProject(f"{space}/TEST_PROJECT")]
            
            def get_experiments(self, space=None):
                class MockExperiment:
                    def __init__(self, identifier):
                        self.identifier = identifier
                        self.registrationDate = "2024-01-01"
                
                return [MockExperiment(f"{space}/TEST_PROJECT/TEST_EXP")]
        
        mock_pybis = MockPybisInstance()
        
        # Test space resolution
        user_space = adapter._get_user_space(mock_pybis)
        print(f"✅ User space resolved: {user_space}")
        
        # Test location resolution
        test_action = Action(
            action="CREATE",
            entity="PROJECT",
            location=Location(space="USER_SPACE"),
            payload=Payload(code="TEST_PROJECT")
        )
        
        resolved_action = adapter._resolve_user_locations(test_action, mock_pybis)
        print(f"✅ Location resolved: {resolved_action.location.space}")
        
        return True
        
    except Exception as e:
        print(f"❌ PybisAdapter test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing chatBIS Structured Workflow Integration")
    print("=" * 60)
    
    tests = [
        test_entity_structurer_with_real_queries,
        test_pybis_adapter_space_resolution,
        test_conversation_engine_structured_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! The structured workflow is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
