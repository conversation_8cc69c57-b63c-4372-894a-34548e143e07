#!/usr/bin/env python3
"""
Test script to verify that the dataset listing hang issue is fixed.
"""

import sys
import os
import time
import signal
sys.path.append('src')

from chatBIS.tools.pybis_tools import PyBISToolManager

class TimeoutError(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutError("Operation timed out")

def test_dataset_listing_with_timeout():
    """Test dataset listing with a timeout to catch hangs."""
    print("=== Testing Dataset Listing (Hang Fix) ===")
    
    manager = PyBISToolManager()
    
    # Set up a timeout of 30 seconds
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(30)  # 30 second timeout
    
    try:
        start_time = time.time()
        print("Starting dataset listing test...")
        
        result = manager._list_datasets_tool("all datasets")
        
        end_time = time.time()
        duration = end_time - start_time
        
        signal.alarm(0)  # Cancel the alarm
        
        print(f"✓ Dataset listing completed in {duration:.2f} seconds")
        
        # Check if we got results
        if "Found" in result and "datasets:" in result:
            print("✓ Got dataset results")
            
            # Show first few lines to verify format
            lines = result.split('\n')[:5]
            print("Sample output:")
            for line in lines:
                print(f"  {line}")
                
            # Check if attachment info is clean
            if "→ Sample:" in result or "→ Experiment:" in result:
                print("✓ Clean attachment info displayed")
            else:
                print("? No attachment info found (might be expected)")
                
        else:
            print(f"? Unexpected result format: {result[:100]}...")
            
        return True
        
    except TimeoutError:
        signal.alarm(0)  # Cancel the alarm
        print("✗ Dataset listing timed out (hang detected)")
        return False
        
    except Exception as e:
        signal.alarm(0)  # Cancel the alarm
        print(f"✗ Dataset listing failed with error: {e}")
        return False

def test_individual_dataset_access():
    """Test accessing individual dataset properties safely."""
    print("\n=== Testing Individual Dataset Access ===")
    
    manager = PyBISToolManager()
    
    try:
        # Get datasets first
        datasets = manager.connection.openbis.get_datasets()
        
        if not datasets:
            print("No datasets found to test")
            return True
            
        # Convert to list if needed
        if hasattr(datasets, '__iter__') and not isinstance(datasets, list):
            datasets_list = list(datasets)
        else:
            datasets_list = datasets
            
        # Test accessing the first dataset safely
        dataset = datasets_list[0]
        print(f"Testing dataset: {getattr(dataset, 'permId', 'unknown')}")
        
        # Test safe attribute access
        print("Testing safe attribute access...")
        
        # Check internal attributes (should not cause API calls)
        has_sample = hasattr(dataset, '_sample') and dataset._sample
        has_experiment = hasattr(dataset, '_experiment') and dataset._experiment
        has_project = hasattr(dataset, '_project') and dataset._project
        
        print(f"  Has sample reference: {has_sample}")
        print(f"  Has experiment reference: {has_experiment}")
        print(f"  Has project reference: {has_project}")
        
        if has_sample:
            sample_id = dataset._sample.get('identifier', 'unknown')
            print(f"  Sample identifier: {sample_id}")
            
        if has_experiment:
            exp_id = dataset._experiment.get('identifier', 'unknown')
            print(f"  Experiment identifier: {exp_id}")
            
        print("✓ Safe attribute access completed")
        return True
        
    except Exception as e:
        print(f"✗ Individual dataset access failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Dataset Hang Fix Verification")
    print("=" * 50)
    
    # Note: signal.alarm only works on Unix-like systems
    if os.name == 'nt':  # Windows
        print("Note: Running on Windows - timeout protection not available")
        print("Will test without timeout protection")
        
        try:
            manager = PyBISToolManager()
            start_time = time.time()
            result = manager._list_datasets_tool("all datasets")
            end_time = time.time()
            
            print(f"✓ Dataset listing completed in {end_time - start_time:.2f} seconds")
            
            if "Found" in result and "datasets:" in result:
                print("✓ Got dataset results")
                lines = result.split('\n')[:3]
                for line in lines:
                    print(f"  {line}")
            
            test_individual_dataset_access()
            
        except Exception as e:
            print(f"✗ Test failed: {e}")
            return False
    else:
        # Unix-like system - can use timeout
        result1 = test_dataset_listing_with_timeout()
        result2 = test_individual_dataset_access()
        
        if not (result1 and result2):
            return False
    
    print("\n" + "=" * 50)
    print("Dataset hang fix verification completed!")
    print("\nKey fix implemented:")
    print("1. ✓ Avoid lazy loading by using internal dataset attributes")
    print("2. ✓ Use _sample, _experiment, _project instead of sample, experiment, project")
    print("3. ✓ Added proper error handling to prevent hangs")
    print("4. ✓ Fallback to attrs.all() if internal attributes not available")
    
    print("\nThe dataset listing should now work without hanging!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
