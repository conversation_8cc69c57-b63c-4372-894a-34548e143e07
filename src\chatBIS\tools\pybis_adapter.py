#!/usr/bin/env python3
"""
PybisAdapter - Deterministic Translator for openBIS Operations

This module provides a deterministic, code-based translator between the
ActionRequest JSON schema and pybis function calls. It handles the execution
of structured openBIS operations without using LLMs, ensuring reliable and
predictable interactions with the openBIS API.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from ..models.entity import ActionRequest, Action, ActionType, EntityType, Metadata

# Configure logging
logger = logging.getLogger(__name__)

# Try to import pybis
try:
    import pybis
    PYBIS_AVAILABLE = True
except ImportError:
    logger.warning("pybis package not available. PybisAdapter will be disabled.")
    PYBIS_AVAILABLE = False


class PybisAdapterError(Exception):
    """Custom exception for PybisAdapter errors."""
    pass


class PybisAdapter:
    """
    Deterministic translator between ActionRequest JSON and pybis function calls.
    
    This class provides methods to execute structured openBIS operations and
    convert pybis objects back to our standardized JSON format.
    """
    
    def __init__(self):
        """Initialize the PybisAdapter."""
        if not PYBIS_AVAILABLE:
            raise ImportError("pybis package not available")

        # Entity type to pybis method mapping
        self.create_methods = {
            'SPACE': 'new_space',
            'PROJECT': 'new_project',
            'EXPERIMENT': 'new_experiment',
            'OBJECT': 'new_sample',  # Note: objects are samples in pybis
            'DATASET': 'new_dataset'
        }

        self.get_methods = {
            'SPACE': 'get_space',
            'PROJECT': 'get_project',
            'EXPERIMENT': 'get_experiment',
            'OBJECT': 'get_sample',
            'DATASET': 'get_dataset'
        }

        self.list_methods = {
            'SPACE': 'get_spaces',
            'PROJECT': 'get_projects',
            'EXPERIMENT': 'get_experiments',
            'OBJECT': 'get_samples',
            'DATASET': 'get_datasets'
        }

    def _get_user_space(self, pybis_instance: 'pybis.Openbis') -> str:
        """Get the user's space name from the username (uppercase)."""
        import os
        username = os.getenv('OPENBIS_USERNAME')
        if username:
            return username.upper()

        # Fallback: try to get from pybis instance
        try:
            if hasattr(pybis_instance, 'username') and pybis_instance.username:
                return pybis_instance.username.upper()
        except:
            pass

        return None

    def _resolve_user_locations(self, action: Action, pybis_instance: 'pybis.Openbis') -> Action:
        """Resolve USER_SPACE, USER_PROJECT, USER_EXPERIMENT placeholders."""
        if not action.location:
            return action

        # Get user's space
        user_space = self._get_user_space(pybis_instance)
        if not user_space:
            raise PybisAdapterError("Cannot determine user space")

        # Create a copy of the action to modify
        action_dict = action.model_dump()
        location = action_dict.get('location', {})

        # Resolve space
        if location.get('space') == 'USER_SPACE':
            location['space'] = user_space

        # Resolve project
        if location.get('project') == 'USER_PROJECT':
            try:
                projects = pybis_instance.get_projects(space=user_space)
                if projects:
                    # Use the most recent project
                    latest_project = max(projects, key=lambda p: getattr(p, 'registrationDate', ''))
                    location['project'] = latest_project.identifier
                else:
                    raise PybisAdapterError(f"No projects found in space {user_space}")
            except Exception as e:
                raise PybisAdapterError(f"Cannot resolve user project: {e}")

        # Resolve experiment
        if location.get('experiment') == 'USER_EXPERIMENT':
            try:
                experiments = pybis_instance.get_experiments(space=user_space)
                if experiments:
                    # Use the most recent experiment
                    latest_experiment = max(experiments, key=lambda e: getattr(e, 'registrationDate', ''))
                    location['experiment'] = latest_experiment.identifier
                else:
                    raise PybisAdapterError(f"No experiments found in space {user_space}")
            except Exception as e:
                raise PybisAdapterError(f"Cannot resolve user experiment: {e}")

        # Reconstruct the action with resolved locations
        from ..models.entity import Action, Location
        if location:
            action_dict['location'] = Location(**location)

        return Action(**action_dict)
    
    def execute_actions(self, request: Union[Dict, ActionRequest], pybis_instance: 'pybis.Openbis') -> List[Dict[str, Any]]:
        """
        Execute a list of actions against openBIS using transactions.
        
        Args:
            request: ActionRequest object or dictionary matching the schema
            pybis_instance: Active pybis.Openbis instance
            
        Returns:
            List of result dictionaries, one for each action
            
        Raises:
            PybisAdapterError: If execution fails
        """
        if not PYBIS_AVAILABLE:
            raise PybisAdapterError("pybis package not available")
        
        # Convert dict to ActionRequest if needed
        if isinstance(request, dict):
            try:
                request = ActionRequest(**request)
            except Exception as e:
                raise PybisAdapterError(f"Invalid ActionRequest format: {e}")
        
        results = []
        created_entities = []  # Track entities for transaction
        
        try:
            # Check if we need a transaction (any CREATE/UPDATE/DELETE operations)
            needs_transaction = request.has_destructive_actions() and request.transaction
            
            if needs_transaction:
                logger.info(f"Starting transaction for {len(request.actions)} actions")
            
            # Process each action
            for i, action in enumerate(request.actions):
                try:
                    logger.info(f"Executing action {i+1}/{len(request.actions)}: {action.action} {action.entity}")
                    
                    if action.action == "CREATE":
                        result = self._execute_create_action(action, pybis_instance, created_entities)
                    elif action.action == "GET":
                        result = self._execute_get_action(action, pybis_instance)
                    elif action.action == "LIST":
                        result = self._execute_list_action(action, pybis_instance)
                    elif action.action == "UPDATE":
                        result = self._execute_update_action(action, pybis_instance, created_entities)
                    elif action.action == "DELETE":
                        result = self._execute_delete_action(action, pybis_instance)
                    else:
                        raise PybisAdapterError(f"Unsupported action type: {action.action}")
                    
                    results.append(result)
                    
                except Exception as e:
                    error_result = {
                        "action": action.action,
                        "entity": action.entity,
                        "success": False,
                        "error": str(e),
                        "action_index": i
                    }
                    results.append(error_result)
                    
                    # If we're in a transaction and an error occurs, we should fail fast
                    if needs_transaction:
                        logger.error(f"Transaction failed at action {i+1}: {e}")
                        raise PybisAdapterError(f"Transaction failed at action {i+1}: {e}")
            
            # Commit transaction if needed
            if needs_transaction and created_entities:
                logger.info(f"Committing transaction with {len(created_entities)} entities")
                transaction = pybis_instance.new_transaction(*created_entities)
                transaction.commit()
                logger.info("Transaction committed successfully")
                
                # Update results with committed entity information
                for result in results:
                    if result.get("success") and "entity_object" in result:
                        entity_obj = result["entity_object"]
                        if hasattr(entity_obj, 'permId'):
                            result["permId"] = entity_obj.permId
                        if hasattr(entity_obj, 'identifier'):
                            result["identifier"] = entity_obj.identifier
                        # Remove the entity object from the result
                        del result["entity_object"]
            
            logger.info(f"Successfully executed {len(request.actions)} actions")
            return results
            
        except Exception as e:
            logger.error(f"Failed to execute actions: {e}")
            # Clean up any created entities that weren't committed
            for entity in created_entities:
                try:
                    if hasattr(entity, 'delete') and not getattr(entity, '_committed', False):
                        entity.delete()
                except:
                    pass  # Ignore cleanup errors
            raise PybisAdapterError(f"Action execution failed: {e}")
    
    def _execute_create_action(self, action: Action, pybis_instance: 'pybis.Openbis', created_entities: List) -> Dict[str, Any]:
        """Execute a CREATE action."""
        if not action.payload:
            raise PybisAdapterError("CREATE action requires payload")

        # Resolve user locations first
        action = self._resolve_user_locations(action, pybis_instance)

        entity_type = action.entity
        method_name = self.create_methods.get(entity_type)

        if not method_name:
            raise PybisAdapterError(f"Unsupported entity type for CREATE: {entity_type}")

        create_method = getattr(pybis_instance, method_name)

        # Build arguments based on pybis method signatures
        kwargs = {}

        if entity_type == "PROJECT":
            # From PyPI docs: new_project(space, code, description='')
            if not action.location or not action.location.space:
                raise PybisAdapterError("PROJECT creation requires space location")

            kwargs['space'] = action.location.space
            kwargs['code'] = action.payload.code
            if action.payload.description:
                kwargs['description'] = action.payload.description

        elif entity_type == "EXPERIMENT":
            # From PyPI docs: new_experiment(code='MY_NEW_EXPERIMENT', type='DEFAULT_EXPERIMENT', project='/MY_SPACE/YEASTS')
            if not action.location or not action.location.project:
                raise PybisAdapterError("EXPERIMENT creation requires project location")

            kwargs['code'] = action.payload.code
            kwargs['type'] = action.payload.type or 'DEFAULT_EXPERIMENT'
            kwargs['project'] = action.location.project
            if action.payload.properties:
                kwargs['props'] = action.payload.properties

        elif entity_type == "OBJECT":
            # From PyPI docs: new_sample(type='YEAST', space='MY_SPACE', experiment='/MY_SPACE/MY_PROJECT/EXPERIMENT_1', props={"name": "some name"})
            kwargs['type'] = action.payload.type or 'CHEMICAL'

            # Set location based on what's available - prefer experiment over project over space
            if action.location:
                if action.location.experiment:
                    kwargs['experiment'] = action.location.experiment
                elif action.location.project:
                    kwargs['project'] = action.location.project
                elif action.location.space:
                    kwargs['space'] = action.location.space

            # Only add code if provided (some sample types auto-generate codes)
            if action.payload.code:
                kwargs['code'] = action.payload.code

            if action.payload.properties:
                kwargs['props'] = action.payload.properties

        elif entity_type == "DATASET":
            # new_dataset(self, type, object=None, props=None, **kwargs)
            if not action.location or not action.location.object:
                raise PybisAdapterError("DATASET creation requires object location")

            kwargs['type'] = action.payload.type or 'RAW_DATA'
            kwargs['object'] = action.location.object
            if action.payload.properties:
                kwargs['props'] = action.payload.properties
            if action.payload.kind:
                kwargs['kind'] = action.payload.kind

        # Create the entity
        logger.debug(f"Creating {entity_type} with args: {kwargs}")
        entity = create_method(**kwargs)

        # Handle relationships
        if action.payload.parents:
            for parent_id in action.payload.parents:
                parent = self._resolve_entity_reference(parent_id, pybis_instance)
                entity.add_parent(parent)

        if action.payload.children:
            for child_id in action.payload.children:
                child = self._resolve_entity_reference(child_id, pybis_instance)
                entity.add_child(child)

        # For datasets, handle file uploads
        if entity_type == "DATASET":
            if action.payload.files:
                for file_path in action.payload.files:
                    entity.add_file(file_path)
            if action.payload.folder:
                entity.add_folder(action.payload.folder)

        # Add to transaction list
        created_entities.append(entity)

        return {
            "action": "CREATE",
            "entity": entity_type,
            "success": True,
            "message": f"Created {entity_type} (will be committed with transaction)",
            "entity_object": entity,  # Temporary, will be removed after commit
            "code": getattr(entity, 'code', None)
        }
    
    def _execute_get_action(self, action: Action, pybis_instance: 'pybis.Openbis') -> Dict[str, Any]:
        """Execute a GET action."""
        if not action.identifier:
            raise PybisAdapterError("GET action requires identifier")
        
        entity_type = action.entity
        method_name = self.get_methods.get(entity_type)
        
        if not method_name:
            raise PybisAdapterError(f"Unsupported entity type for GET: {entity_type}")
        
        get_method = getattr(pybis_instance, method_name)
        
        # Determine which identifier to use
        if action.identifier.permId:
            entity = get_method(action.identifier.permId)
        elif action.identifier.identifier:
            entity = get_method(action.identifier.identifier)
        else:
            raise PybisAdapterError("No valid identifier provided")
        
        if not entity:
            raise PybisAdapterError(f"{entity_type} not found")
        
        # Convert to our JSON format
        json_response = self.pybis_to_json_response(entity)
        
        return {
            "action": "GET",
            "entity": entity_type,
            "success": True,
            "data": json_response
        }
    
    def _execute_list_action(self, action: Action, pybis_instance: 'pybis.Openbis') -> Dict[str, Any]:
        """Execute a LIST action."""
        entity_type = action.entity
        method_name = self.list_methods.get(entity_type)
        
        if not method_name:
            raise PybisAdapterError(f"Unsupported entity type for LIST: {entity_type}")
        
        list_method = getattr(pybis_instance, method_name)
        
        # Execute the list operation
        entities = list_method()
        
        # Convert to our JSON format
        json_responses = [self.pybis_to_json_response(entity) for entity in entities]
        
        return {
            "action": "LIST",
            "entity": entity_type,
            "success": True,
            "count": len(json_responses),
            "data": json_responses
        }
    
    def _execute_update_action(self, action: Action, pybis_instance: 'pybis.Openbis', created_entities: List) -> Dict[str, Any]:
        """Execute an UPDATE action."""
        if not action.identifier or not action.payload:
            raise PybisAdapterError("UPDATE action requires both identifier and payload")
        
        # First get the entity
        get_action = Action(action="GET", entity=action.entity, identifier=action.identifier)
        get_result = self._execute_get_action(get_action, pybis_instance)
        
        if not get_result["success"]:
            raise PybisAdapterError(f"Could not find entity to update: {get_result.get('error', 'Unknown error')}")
        
        # Get the actual entity object for updating
        entity_type = action.entity
        method_name = self.get_methods.get(entity_type)
        get_method = getattr(pybis_instance, method_name)
        
        if action.identifier.permId:
            entity = get_method(action.identifier.permId)
        else:
            entity = get_method(action.identifier.identifier)
        
        # Apply updates from payload
        payload_dict = action.payload.model_dump(exclude_none=True)
        
        if 'properties' in payload_dict:
            for key, value in payload_dict['properties'].items():
                entity.set_property(key, value)
        
        if 'description' in payload_dict:
            entity.description = payload_dict['description']
        
        # Add to transaction list
        created_entities.append(entity)
        
        return {
            "action": "UPDATE",
            "entity": entity_type,
            "success": True,
            "message": f"Updated {entity_type} (will be committed with transaction)",
            "entity_object": entity
        }
    
    def _execute_delete_action(self, action: Action, pybis_instance: 'pybis.Openbis') -> Dict[str, Any]:
        """Execute a DELETE action."""
        if not action.identifier:
            raise PybisAdapterError("DELETE action requires identifier")
        
        # First get the entity to ensure it exists
        get_action = Action(action="GET", entity=action.entity, identifier=action.identifier)
        get_result = self._execute_get_action(get_action, pybis_instance)
        
        if not get_result["success"]:
            raise PybisAdapterError(f"Could not find entity to delete: {get_result.get('error', 'Unknown error')}")
        
        # Get the actual entity object for deletion
        entity_type = action.entity
        method_name = self.get_methods.get(entity_type)
        get_method = getattr(pybis_instance, method_name)
        
        if action.identifier.permId:
            entity = get_method(action.identifier.permId)
        else:
            entity = get_method(action.identifier.identifier)
        
        # Delete the entity
        entity.delete()
        
        return {
            "action": "DELETE",
            "entity": entity_type,
            "success": True,
            "message": f"Deleted {entity_type}",
            "identifier": action.identifier.model_dump()
        }
    
    def _resolve_entity_reference(self, reference: str, pybis_instance: 'pybis.Openbis') -> Any:
        """Resolve an entity reference (identifier or permId) to a pybis object."""
        # Try to determine entity type from identifier format
        if reference.startswith('/') and reference.count('/') >= 2:
            # This looks like a full identifier
            parts = reference.split('/')
            if len(parts) == 3:  # /SPACE/PROJECT
                return pybis_instance.get_project(reference)
            elif len(parts) == 4:  # /SPACE/PROJECT/EXPERIMENT
                return pybis_instance.get_experiment(reference)
            else:
                # Could be an object/sample
                return pybis_instance.get_sample(reference)
        else:
            # Assume it's a permId, try different entity types
            for get_method in [pybis_instance.get_sample, pybis_instance.get_experiment, 
                             pybis_instance.get_project, pybis_instance.get_dataset]:
                try:
                    entity = get_method(reference)
                    if entity:
                        return entity
                except Exception:
                    continue
            
            raise PybisAdapterError(f"Could not resolve entity reference: {reference}")
    
    def pybis_to_json_response(self, pybis_object: Any) -> Dict[str, Any]:
        """
        Convert a pybis object to our standardized JSON Action format.
        
        Args:
            pybis_object: A pybis entity object (Space, Project, Experiment, Sample, Dataset)
            
        Returns:
            Dictionary conforming to the Action schema with action: "GET"
        """
        if not pybis_object:
            raise PybisAdapterError("Cannot convert None object to JSON")
        
        # Determine entity type
        entity_type = self._get_entity_type_from_pybis_object(pybis_object)
        
        # Build metadata - convert to simple dict to avoid serialization issues
        metadata_data = {
            "permId": str(getattr(pybis_object, 'permId', '')),
            "identifier": str(getattr(pybis_object, 'identifier', '')),
            "registrator": str(getattr(pybis_object, 'registrator', '')) if getattr(pybis_object, 'registrator', None) else None,
            "registrationDate": str(getattr(pybis_object, 'registrationDate', '')) if getattr(pybis_object, 'registrationDate', None) else None,
            "modifier": str(getattr(pybis_object, 'modifier', '')) if getattr(pybis_object, 'modifier', None) else None,
            "modificationDate": str(getattr(pybis_object, 'modificationDate', '')) if getattr(pybis_object, 'modificationDate', None) else None
        }

        # Build payload - ensure all values are serializable
        payload_data = {
            "code": str(getattr(pybis_object, 'code', '')) if getattr(pybis_object, 'code', None) else None,
            "type": str(getattr(pybis_object, 'type', '')) if getattr(pybis_object, 'type', None) else None,
            "description": str(getattr(pybis_object, 'description', '')) if getattr(pybis_object, 'description', None) else None
        }

        # Handle properties - convert to simple dict
        try:
            props = getattr(pybis_object, 'props', {})
            if props:
                # Convert PropertyHolder or similar objects to simple dict
                if hasattr(props, 'items'):
                    payload_data["properties"] = {str(k): str(v) for k, v in props.items()}
                elif hasattr(props, '__dict__'):
                    payload_data["properties"] = {str(k): str(v) for k, v in props.__dict__.items() if not k.startswith('_')}
                else:
                    payload_data["properties"] = {}
            else:
                payload_data["properties"] = {}
        except Exception as e:
            logger.warning(f"Error processing properties: {e}")
            payload_data["properties"] = {}

        # Add relationships if available - ensure serializable
        try:
            parents = getattr(pybis_object, 'parents', None)
            if parents and hasattr(parents, '__iter__'):
                payload_data["parents"] = [str(p.identifier) if hasattr(p, 'identifier') else str(p) for p in parents]
            else:
                payload_data["parents"] = []
        except Exception as e:
            logger.warning(f"Error processing parents: {e}")
            payload_data["parents"] = []

        try:
            children = getattr(pybis_object, 'children', None)
            if children and hasattr(children, '__iter__'):
                payload_data["children"] = [str(c.identifier) if hasattr(c, 'identifier') else str(c) for c in children]
            else:
                payload_data["children"] = []
        except Exception as e:
            logger.warning(f"Error processing children: {e}")
            payload_data["children"] = []

        return {
            "action": "GET",
            "entity": entity_type,
            "metadata": metadata_data,
            "payload": payload_data
        }
    
    def _get_entity_type_from_pybis_object(self, pybis_object: Any) -> str:
        """Determine the entity type from a pybis object."""
        class_name = pybis_object.__class__.__name__.lower()
        
        if 'space' in class_name:
            return "SPACE"
        elif 'project' in class_name:
            return "PROJECT"
        elif 'experiment' in class_name:
            return "EXPERIMENT"
        elif 'sample' in class_name:
            return "OBJECT"  # Samples are objects in our schema
        elif 'dataset' in class_name:
            return "DATASET"
        else:
            # Fallback: try to determine from identifier structure
            identifier = getattr(pybis_object, 'identifier', '')
            if identifier:
                parts = identifier.split('/')
                if len(parts) == 2:
                    return "SPACE"
                elif len(parts) == 3:
                    return "PROJECT"
                elif len(parts) == 4:
                    return "EXPERIMENT"
                else:
                    return "OBJECT"
            
            raise PybisAdapterError(f"Cannot determine entity type for object: {class_name}")
