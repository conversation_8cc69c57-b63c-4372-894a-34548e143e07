#!/usr/bin/env python3
"""
Direct test of the structured workflow components without database dependencies.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_direct_structured_workflow():
    """Test the structured workflow components directly."""
    print("=== Testing Direct Structured Workflow ===")
    
    try:
        from chatBIS.tools.entity_structurer import EntityStructurer
        from chatBIS.tools.pybis_adapter import PybisAdapter
        from chatBIS.models.entity import ActionRequest
        
        # Test EntityStructurer
        structurer = EntityStructurer(model="qwen3")
        
        # Test the problematic queries from the conversation
        test_queries = [
            'create a new sample "CHAT_SAMPLE"',
            'Create a new project called CHAT_PROJECT'
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing: '{query}'")
            
            # Structure the request
            structured = structurer.structure_user_request(query)
            print(f"✅ Structured: {structured}")
            
            # Validate with Pydantic
            request = ActionRequest(**structured)
            print(f"✅ Validated: {len(request.actions)} actions")
            
            # Generate confirmation
            confirmation = structurer.format_confirmation_summary(structured)
            print(f"✅ Confirmation generated ({len(confirmation)} chars)")
            print(f"📝 Confirmation preview: {confirmation[:200]}...")
            
            # Test space resolution (mock)
            adapter = PybisAdapter()
            
            class MockPybisInstance:
                def __init__(self):
                    self.username = "cmadaria"
                
                def get_projects(self, space=None):
                    class MockProject:
                        def __init__(self):
                            self.identifier = f"{space}/TEST_PROJECT"
                            self.registrationDate = "2024-01-01"
                    return [MockProject()]
                
                def get_experiments(self, space=None):
                    class MockExperiment:
                        def __init__(self):
                            self.identifier = f"{space}/TEST_PROJECT/TEST_EXP"
                            self.registrationDate = "2024-01-01"
                    return [MockExperiment()]
            
            mock_pybis = MockPybisInstance()
            
            # Test location resolution
            for action in request.actions:
                if action.location:
                    resolved_action = adapter._resolve_user_locations(action, mock_pybis)
                    print(f"✅ Location resolved: {resolved_action.location.model_dump()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pybis_method_signatures():
    """Test that we're using the correct pybis method signatures."""
    print("\n=== Testing PyBIS Method Signatures ===")
    
    try:
        from chatBIS.tools.pybis_adapter import PybisAdapter
        from chatBIS.models.entity import Action, Location, Payload
        
        adapter = PybisAdapter()
        
        # Test project creation arguments
        project_action = Action(
            action="CREATE",
            entity="PROJECT",
            location=Location(space="CMADARIA"),
            payload=Payload(code="TEST_PROJECT", description="Test project")
        )
        
        print("✅ Project action created")
        
        # Test experiment creation arguments
        experiment_action = Action(
            action="CREATE",
            entity="EXPERIMENT",
            location=Location(project="/CMADARIA/TEST_PROJECT"),
            payload=Payload(code="TEST_EXP", type="DEFAULT_EXPERIMENT")
        )
        
        print("✅ Experiment action created")
        
        # Test sample creation arguments
        sample_action = Action(
            action="CREATE",
            entity="OBJECT",
            location=Location(experiment="/CMADARIA/TEST_PROJECT/TEST_EXP"),
            payload=Payload(code="TEST_SAMPLE", type="CHEMICAL")
        )
        
        print("✅ Sample action created")
        
        return True
        
    except Exception as e:
        print(f"❌ Method signature test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run direct tests."""
    print("🧪 Testing Structured Workflow Direct Execution")
    print("=" * 60)
    
    tests = [
        test_direct_structured_workflow,
        test_pybis_method_signatures
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All direct tests passed! The structured workflow is ready.")
        return 0
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
