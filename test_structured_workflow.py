#!/usr/bin/env python3
"""
Test script for the new structured interaction workflow in chatBIS.

This script validates the implementation of the ActionRequest schema,
EntityStructurer, PybisAdapter, and the integrated ConversationEngine workflow.
"""

import sys
import os
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_action_request_schema():
    """Test the ActionRequest Pydantic models."""
    print("=== Testing ActionRequest Schema ===")
    
    try:
        from chatBIS.models.entity import ActionRequest, Action, Identifier, Location, Payload
        
        # Test basic CREATE action
        create_action = {
            "actions": [
                {
                    "action": "CREATE",
                    "entity": "PROJECT",
                    "location": {
                        "space": "/TEST_SPACE"
                    },
                    "payload": {
                        "code": "TEST_PROJECT_001",
                        "type": "DEFAULT",
                        "properties": {
                            "DESCRIPTION": "Test project created via structured workflow"
                        }
                    }
                }
            ]
        }
        
        # Validate with Pydantic
        request = ActionRequest(**create_action)
        print(f"✅ CREATE action validation passed: {request.actions[0].payload.code}")
        
        # Test GET action
        get_action = {
            "actions": [
                {
                    "action": "GET",
                    "entity": "PROJECT",
                    "identifier": {
                        "identifier": "/TEST_SPACE/TEST_PROJECT_001"
                    }
                }
            ]
        }
        
        request = ActionRequest(**get_action)
        print(f"✅ GET action validation passed: {request.actions[0].identifier.identifier}")
        
        # Test complex multi-action request
        complex_request = {
            "actions": [
                {
                    "action": "CREATE",
                    "entity": "EXPERIMENT",
                    "location": {
                        "project": "/TEST_SPACE/TEST_PROJECT_001"
                    },
                    "payload": {
                        "code": "TEST_EXP_001",
                        "type": "COLLECTION_EXPERIMENT",
                        "properties": {
                            "NOTES": "Test experiment"
                        }
                    }
                },
                {
                    "action": "CREATE",
                    "entity": "OBJECT",
                    "location": {
                        "experiment": "/TEST_SPACE/TEST_PROJECT_001/TEST_EXP_001"
                    },
                    "payload": {
                        "code": "TEST_SAMPLE_001",
                        "type": "CHEMICAL",
                        "properties": {
                            "CHEMICAL_NAME": "Test Chemical"
                        }
                    }
                }
            ]
        }
        
        request = ActionRequest(**complex_request)
        print(f"✅ Complex multi-action validation passed: {len(request.actions)} actions")
        print(f"   - Has destructive actions: {request.has_destructive_actions()}")
        print(f"   - Summary: {request.summary()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False


def test_entity_structurer():
    """Test the EntityStructurer component."""
    print("\n=== Testing EntityStructurer ===")
    
    try:
        from chatBIS.tools.entity_structurer import EntityStructurer
        
        structurer = EntityStructurer(model="qwen3")
        
        # Test natural language to JSON conversion
        test_queries = [
            "Create a new project called TEST_PROJECT in space MY_LAB",
            "Make a sample named SAMPLE_001 of type CHEMICAL in experiment /MY_LAB/PROJECT_A/EXP_001",
            "Get the project /MY_LAB/PROJECT_A",
            "List all samples"
        ]
        
        for query in test_queries:
            print(f"\nTesting query: '{query}'")
            try:
                result = structurer.structure_user_request(query)
                print(f"✅ Structured successfully: {len(result.get('actions', []))} actions")
                
                # Test confirmation summary
                summary = structurer.format_confirmation_summary(result)
                print(f"✅ Confirmation summary generated ({len(summary)} chars)")
                
            except Exception as e:
                print(f"❌ Failed to structure query: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ EntityStructurer test failed: {e}")
        return False


def test_pybis_adapter_mock():
    """Test PybisAdapter with mock data (no actual pybis connection)."""
    print("\n=== Testing PybisAdapter (Mock) ===")
    
    try:
        from chatBIS.tools.pybis_adapter import PybisAdapter
        
        adapter = PybisAdapter()
        
        # Test JSON response formatting
        class MockPybisObject:
            def __init__(self):
                self.permId = "TEST_PERM_ID_123"
                self.identifier = "/TEST_SPACE/TEST_PROJECT"
                self.code = "TEST_PROJECT"
                self.type = "DEFAULT"
                self.props = {"DESCRIPTION": "Test project"}
                self.registrator = "test_user"
                self.registrationDate = "2024-01-01T10:00:00Z"
                self.modifier = None
                self.modificationDate = None
        
        mock_object = MockPybisObject()
        json_response = adapter.pybis_to_json_response(mock_object)
        
        print(f"✅ JSON response conversion successful")
        print(f"   - Entity type: {json_response['entity']}")
        print(f"   - PermId: {json_response['metadata']['permId']}")
        print(f"   - Code: {json_response['payload']['code']}")
        
        return True
        
    except Exception as e:
        print(f"❌ PybisAdapter test failed: {e}")
        return False


def test_conversation_engine_routing():
    """Test the ConversationEngine routing logic."""
    print("\n=== Testing ConversationEngine Routing ===")
    
    try:
        from chatBIS.query.conversation_engine import ConversationEngine
        
        # Initialize with test data directory
        test_data_dir = "data/processed"  # This might not exist, but that's ok for routing test
        
        engine = ConversationEngine(data_dir=test_data_dir, model="qwen3")
        
        # Test routing decisions
        test_cases = [
            ("create a new project", "structured_interaction"),
            ("make a sample", "structured_interaction"), 
            ("how to create a project", "rag"),
            ("list all projects", "function_call"),
            ("what is openBIS", "rag"),
            ("connect to openbis", "function_call"),
            ("create space TEST_SPACE", "structured_interaction"),
            ("update the project /MY_LAB/PROJECT_A", "structured_interaction")
        ]
        
        # We can't easily test the full routing without mocking the graph execution,
        # but we can test the router logic patterns
        for query, expected_decision in test_cases:
            print(f"Testing query: '{query}' -> Expected: {expected_decision}")
            
            # This is a simplified test - in reality we'd need to mock the full state
            query_lower = query.lower()
            
            # Replicate the router logic (following the actual order in ConversationEngine)
            rag_patterns = [
                'how to', 'how can i', 'how do i',
                'what is', 'what are',
                'can you explain', 'tell me about', 'explain how', 'show me how',
                'help me', 'i want to know', 'i need to know'
            ]

            structured_patterns = [
                'create a', 'create new', 'make a', 'make new', 'add a', 'add new',
                'update the', 'modify the', 'change the', 'edit the'
            ]

            entity_creation_patterns = [
                'create space', 'create project', 'create experiment', 'create sample'
            ]

            connection_keywords = ['connect', 'login', 'disconnect', 'logout']

            # Check in the same order as the actual router
            has_rag = any(pattern in query_lower for pattern in rag_patterns)
            has_structured = any(pattern in query_lower for pattern in structured_patterns)
            has_entity_creation = any(pattern in query_lower for pattern in entity_creation_patterns)
            has_connection = any(keyword in query_lower for keyword in connection_keywords)

            if has_rag:
                actual_decision = "rag"
            elif has_structured or has_entity_creation:
                actual_decision = "structured_interaction"
            elif has_connection:
                actual_decision = "function_call"
            else:
                actual_decision = "function_call"  # Default for action queries
            
            if actual_decision == expected_decision:
                print(f"✅ Routing correct: {actual_decision}")
            else:
                print(f"⚠️ Routing mismatch: got {actual_decision}, expected {expected_decision}")
        
        return True
        
    except Exception as e:
        print(f"❌ ConversationEngine routing test failed: {e}")
        return False


def test_integration():
    """Test basic integration of all components."""
    print("\n=== Testing Integration ===")
    
    try:
        # Test that all imports work together
        from chatBIS.models.entity import ActionRequest
        from chatBIS.tools.entity_structurer import EntityStructurer
        from chatBIS.tools.pybis_adapter import PybisAdapter
        from chatBIS.query.conversation_engine import ConversationEngine
        
        print("✅ All imports successful")
        
        # Test end-to-end workflow (without actual pybis connection)
        structurer = EntityStructurer(model="qwen3")
        
        # Structure a request
        query = "Create a project called INTEGRATION_TEST in space TEST_SPACE"
        structured_request = structurer.structure_user_request(query)
        
        # Validate the request
        request = ActionRequest(**structured_request)
        print(f"✅ End-to-end structuring successful: {request.actions[0].payload.code}")
        
        # Generate confirmation
        confirmation = structurer.format_confirmation_summary(structured_request)
        print(f"✅ Confirmation summary generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing Structured Interaction Workflow Implementation")
    print("=" * 60)
    
    tests = [
        test_action_request_schema,
        test_entity_structurer,
        test_pybis_adapter_mock,
        test_conversation_engine_routing,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The structured workflow implementation is ready.")
        return 0
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
